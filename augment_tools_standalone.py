#!/usr/bin/env python3
"""
AugmentCode-Free 独立工具
提取的核心功能：clean-db, modify-ids, run-all

使用方法:
    python augment_tools_standalone.py clean-db --ide vscode --keyword augment
    python augment_tools_standalone.py modify-ids --ide cursor
    python augment_tools_standalone.py run-all --ide windsurf
"""

import argparse
import json
import os
import platform
import shutil
import sqlite3
import uuid
import xml.etree.ElementTree as ET
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Union

# 尝试导入 colorama 用于彩色输出
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    IS_COLORAMA_AVAILABLE = True
except ImportError:
    IS_COLORAMA_AVAILABLE = False


class IDEType(Enum):
    """支持的IDE类型"""
    VSCODE = "vscode"
    CURSOR = "cursor"
    WINDSURF = "windsurf"
    JETBRAINS = "jetbrains"


# === 输出函数 ===
def print_message(prefix: str, message: str, color_code: str = "") -> None:
    """带颜色的消息输出"""
    if IS_COLORAMA_AVAILABLE and color_code:
        print(f"{color_code}{prefix}{Style.RESET_ALL} {message}")
    else:
        print(f"{prefix} {message}")


def print_info(message: str) -> None:
    """信息消息"""
    color = Fore.BLUE if IS_COLORAMA_AVAILABLE else ""
    print_message("[INFO]", message, color)


def print_success(message: str) -> None:
    """成功消息"""
    color = Fore.GREEN if IS_COLORAMA_AVAILABLE else ""
    print_message("[SUCCESS]", message, color)


def print_warning(message: str) -> None:
    """警告消息"""
    color = Fore.YELLOW if IS_COLORAMA_AVAILABLE else ""
    print_message("[WARNING]", message, color)


def print_error(message: str) -> None:
    """错误消息"""
    color = Fore.RED if IS_COLORAMA_AVAILABLE else ""
    print_message("[ERROR]", message, color)


# === 工具函数 ===
def parse_ide_type(ide_name: str) -> IDEType:
    """解析IDE名称为IDEType枚举"""
    ide_name_lower = ide_name.lower()
    if ide_name_lower in ['vscode', 'vs-code', 'code']:
        return IDEType.VSCODE
    elif ide_name_lower in ['cursor']:
        return IDEType.CURSOR
    elif ide_name_lower in ['windsurf']:
        return IDEType.WINDSURF
    elif ide_name_lower in ['jetbrains', 'pycharm', 'intellij', 'idea', 'webstorm', 'phpstorm']:
        return IDEType.JETBRAINS
    else:
        raise ValueError(f"不支持的IDE: {ide_name}")


def get_ide_display_name(ide_type: IDEType) -> str:
    """获取IDE显示名称"""
    display_names = {
        IDEType.VSCODE: "VS Code",
        IDEType.CURSOR: "Cursor", 
        IDEType.WINDSURF: "Windsurf",
        IDEType.JETBRAINS: "JetBrains"
    }
    return display_names.get(ide_type, ide_type.value)


def create_backup(file_path: Union[str, Path]) -> Union[Path, None]:
    """创建文件备份"""
    original_path = Path(file_path)
    if not original_path.exists():
        print_error(f"备份文件不存在: {original_path}")
        return None

    backup_path = original_path.with_suffix(original_path.suffix + ".backup")
    try:
        shutil.copy2(original_path, backup_path)
        print_success(f"备份创建成功: {backup_path}")
        return backup_path
    except Exception as e:
        print_error(f"创建备份失败 {original_path}: {e}")
        return None


def generate_new_machine_id() -> str:
    """生成新的64字符机器ID"""
    return uuid.uuid4().hex + uuid.uuid4().hex


def generate_new_device_id() -> str:
    """生成新的设备ID"""
    return str(uuid.uuid4())


# === IDE路径获取 ===
def get_ide_paths(ide_type: IDEType) -> Optional[Dict[str, Path]]:
    """获取IDE配置文件路径"""
    system = platform.system()
    paths: Dict[str, Path] = {}

    try:
        if ide_type == IDEType.VSCODE:
            if system == "Windows":
                appdata = os.environ.get("APPDATA")
                if not appdata:
                    print_error("未找到APPDATA环境变量")
                    return None
                base_dir = Path(appdata) / "Code" / "User"
            elif system == "Darwin":  # macOS
                base_dir = Path.home() / "Library" / "Application Support" / "Code" / "User"
            elif system == "Linux":
                base_dir = Path.home() / ".config" / "Code" / "User"
            else:
                print_error(f"不支持的操作系统: {system}")
                return None

            paths["state_db"] = base_dir / "globalStorage" / "state.vscdb"
            paths["storage_json"] = base_dir / "globalStorage" / "storage.json"

        elif ide_type == IDEType.CURSOR:
            if system == "Windows":
                appdata = os.environ.get("APPDATA")
                if not appdata:
                    print_error("未找到APPDATA环境变量")
                    return None
                base_dir = Path(appdata) / "Cursor" / "User"
            elif system == "Darwin":  # macOS
                base_dir = Path.home() / ".cursor"
            elif system == "Linux":
                base_dir = Path.home() / ".cursor"
            else:
                print_error(f"不支持的操作系统: {system}")
                return None

            paths["state_db"] = base_dir / "globalStorage" / "state.vscdb"
            paths["storage_json"] = base_dir / "globalStorage" / "storage.json"

        elif ide_type == IDEType.WINDSURF:
            windsurf_paths = detect_windsurf_paths()
            if not windsurf_paths:
                print_error("无法找到Windsurf数据目录")
                return None
            paths.update(windsurf_paths)

        elif ide_type == IDEType.JETBRAINS:
            print_info("JetBrains产品使用SessionID配置")
            return {}

        return paths
    except Exception as e:
        print_error(f"获取{ide_type.value}路径失败: {e}")
        return None


def detect_windsurf_paths() -> Dict[str, Path]:
    """检测Windsurf路径"""
    home = Path.home()
    system = platform.system()

    # 构建标准路径
    if system == "Windows":
        appdata = os.environ.get("APPDATA")
        if appdata:
            standard_base = Path(appdata) / "Windsurf"
        else:
            standard_base = None
    elif system == "Darwin":  # macOS
        standard_base = home / "Library" / "Application Support" / "Windsurf"
    else:  # Linux
        standard_base = home / ".config" / "Windsurf"

    # 可能的基础目录
    possible_base_dirs = []
    if standard_base:
        possible_base_dirs.append(standard_base)
    
    possible_base_dirs.extend([
        home / ".codeium" / "windsurf",
        home / ".windsurf",
        home / ".codeium" / "windsurf" / "User",
    ])

    # 可能的子目录结构
    possible_structures = [
        ("User/globalStorage", "extensions"),
        ("User/globalStorage", "User/extensions"),
        ("globalStorage", "extensions"),
        ("data/User/globalStorage", "data/extensions"),
    ]

    for base_dir in possible_base_dirs:
        if not base_dir.exists():
            continue

        print_info(f"检查Windsurf路径: {base_dir}")

        for storage_path, ext_path in possible_structures:
            state_db = base_dir / storage_path / "state.vscdb"
            storage_json = base_dir / storage_path / "storage.json"
            extensions = base_dir / ext_path

            if state_db.exists() or storage_json.exists():
                print_success(f"找到Windsurf数据目录: {base_dir}")
                return {
                    "state_db": state_db,
                    "storage_json": storage_json,
                    "extensions": extensions
                }

    print_warning("未找到Windsurf数据文件")
    return {}


# === 数据库清理功能 ===
def clean_ide_database(ide_type: IDEType, keyword: str = "augment") -> bool:
    """清理IDE数据库"""
    ide_name = get_ide_display_name(ide_type)
    print_info(f"开始清理 {ide_name} 数据库 (关键字: '{keyword}')")

    # JetBrains产品不需要数据库清理
    if ide_type == IDEType.JETBRAINS:
        print_info(f"{ide_name} 产品不需要数据库清理，跳过此步骤")
        return True

    paths = get_ide_paths(ide_type)
    if not paths:
        print_error(f"无法确定 {ide_name} 路径")
        return False

    db_path = paths.get("state_db")
    if not db_path:
        print_error(f"未找到 {ide_name} state.vscdb 路径")
        return False

    return clean_vscode_database(db_path, keyword)


def clean_vscode_database(db_path: Path, keyword: str = "augment") -> bool:
    """清理SQLite数据库"""
    if not db_path.exists():
        print_error(f"数据库文件未找到: {db_path}")
        return False

    print_info(f"尝试清理数据库: {db_path}")
    print_info(f"目标清理关键字: '{keyword}'")

    backup_path = None
    try:
        # 创建备份
        print_info("正在备份数据库...")
        backup_path = create_backup(db_path)
        if not backup_path:
            return False
        print_success(f"数据库备份成功: {backup_path}")

        # 连接数据库
        print_info(f"连接到数据库: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print_success("成功连接到数据库")

        # 查找要删除的条目
        query_select = f"SELECT key FROM ItemTable WHERE key LIKE ?"
        like_pattern = f"%{keyword}%"

        print_info(f"搜索包含关键字 '{keyword}' 的条目...")
        cursor.execute(query_select, (like_pattern,))
        entries_to_delete = cursor.fetchall()

        num_entries_to_delete = len(entries_to_delete)

        if num_entries_to_delete == 0:
            print_success(f"未找到包含关键字 '{keyword}' 的条目。数据库已经是干净的")
            conn.close()
            return True

        print_info(f"找到 {num_entries_to_delete} 个包含 '{keyword}' 的条目:")
        for entry in entries_to_delete[:5]:  # 显示前5个条目
            print_info(f"  - {entry[0]}")
        if num_entries_to_delete > 5:
            print_info(f"  ... 还有 {num_entries_to_delete - 5} 个条目")

        # 删除条目
        query_delete = f"DELETE FROM ItemTable WHERE key LIKE ?"
        print_info(f"正在删除包含 '{keyword}' 的条目...")
        cursor.execute(query_delete, (like_pattern,))
        conn.commit()

        deleted_rows = cursor.rowcount
        if deleted_rows == num_entries_to_delete:
            print_success(f"成功删除 {deleted_rows} 个包含 '{keyword}' 的条目")
        else:
            print_warning(f"尝试删除 {num_entries_to_delete} 个条目，但实际删除了 {deleted_rows} 个")

        conn.close()
        print_success("数据库清理完成")
        return True

    except sqlite3.Error as e:
        print_error(f"SQLite 错误: {e}")
        if backup_path and backup_path.exists():
            print_warning(f"尝试从备份恢复数据库: {backup_path}")
            try:
                shutil.copy2(backup_path, db_path)
                print_success("数据库已从备份成功恢复")
            except Exception as restore_e:
                print_error(f"从备份恢复数据库失败: {restore_e}")
        return False
    except Exception as e:
        print_error(f"发生意外错误: {e}")
        return False


# === 遥测ID修改功能 ===
def modify_ide_telemetry_ids(ide_type: IDEType) -> bool:
    """修改IDE遥测ID"""
    ide_name = get_ide_display_name(ide_type)
    print_info(f"开始修改 {ide_name} 遥测 ID")

    # JetBrains产品使用不同的处理方式
    if ide_type == IDEType.JETBRAINS:
        return modify_all_jetbrains_session_ids()

    paths = get_ide_paths(ide_type)
    if not paths:
        print_error(f"无法确定 {ide_name} 路径")
        return False

    storage_path = paths.get("storage_json")
    if not storage_path:
        print_error(f"未找到 {ide_name} storage.json 路径")
        return False

    return modify_vscode_telemetry_ids(storage_path)


def modify_vscode_telemetry_ids(storage_json_path: Path) -> bool:
    """修改storage.json中的遥测ID"""
    print_info(f"尝试修改遥测 ID: {storage_json_path}")

    if not storage_json_path.exists():
        print_error(f"存储文件未找到: {storage_json_path}")
        return False

    backup_path = create_backup(storage_json_path)
    if not backup_path:
        print_error("创建备份失败。中止遥测 ID 修改")
        return False

    try:
        # 生成新ID
        new_machine_id = generate_new_machine_id()
        new_device_id = generate_new_device_id()

        print_info(f"生成新的 machineId: {new_machine_id}")
        print_info(f"生成新的 devDeviceId: {new_device_id}")

        # 读取JSON文件
        with open(storage_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 修改ID
        modified = False
        if 'machineId' in data:
            if data['machineId'] != new_machine_id:
                data['machineId'] = new_machine_id
                print_info("更新了根级 machineId")
                modified = True
            else:
                print_info("根级 machineId 已经是新值")

        if 'telemetry' in data and isinstance(data['telemetry'], dict):
            if 'machineId' in data['telemetry']:
                if data['telemetry']['machineId'] != new_machine_id:
                    data['telemetry']['machineId'] = new_machine_id
                    print_info("更新了遥测 machineId")
                    modified = True
                else:
                    print_info("遥测 machineId 已经是新值")

            if 'devDeviceId' in data['telemetry']:
                if data['telemetry']['devDeviceId'] != new_device_id:
                    data['telemetry']['devDeviceId'] = new_device_id
                    print_info("更新了 devDeviceId")
                    modified = True
                else:
                    print_info("devDeviceId 已经是新值")

        if not modified:
            print_info("未找到相关遥测 ID 或 ID 已经匹配新值。内容未更改")
            return True

        # 写回文件
        with open(storage_json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4)

        print_success(f"成功修改遥测 ID: {storage_json_path}")
        return True

    except json.JSONDecodeError as e:
        print_error(f"JSON解码错误: {e}")
    except IOError as e:
        print_error(f"IO错误: {e}")
    except Exception as e:
        print_error(f"遥测 ID 修改过程中发生意外错误: {e}")

    # 如果出错，尝试从备份恢复
    if backup_path and backup_path.exists():
        print_info(f"尝试从备份恢复存储文件: {backup_path}")
        try:
            shutil.copy2(backup_path, storage_json_path)
            print_success("存储文件已从备份成功恢复")
        except Exception as restore_e:
            print_error(f"从备份恢复存储文件失败: {restore_e}")
    return False
